param(
    [Parameter(Mandatory=$true)]
    [string[]]$SourceDirectories,
    
    [Parameter(Mandatory=$true)]
    [string]$ArchiveDirectory,
    
    [Parameter(Mandatory=$false)]
    [int]$DaysOld = 30,
    
    [Parameter(Mandatory=$false)]
    [string[]]$FileExtensions = @("*.log", "*.trc"),
    
    [Parameter(Mandatory=$false)]
    [switch]$WhatIf,
    
    [Parameter(Mandatory=$false)]
    [switch]$Verbose
)

<#
.SYNOPSIS
    Archives old log and trace files from specified directories.

.DESCRIPTION
    This script searches specified directories for log and trace files older than a specified number of days,
    archives them into zip files in an archive directory, and then deletes the original files.

.PARAMETER SourceDirectories
    Array of directories to search for log files. Mandatory parameter.

.PARAMETER ArchiveDirectory
    Directory where archived zip files will be stored. Mandatory parameter.

.PARAMETER DaysOld
    Number of days old files must be to be archived. Default is 30 days.

.PARAMETER FileExtensions
    Array of file extensions to search for. Default is @("*.log", "*.trc").

.PARAMETER WhatIf
    Shows what would be done without actually performing the operations.

.PARAMETER Verbose
    Enables verbose output showing detailed information about operations.

.EXAMPLE
    .\WebLogMaintenance.ps1 -SourceDirectories @("C:\Logs", "C:\App\Logs") -ArchiveDirectory "C:\Archive"
    
.EXAMPLE
    .\WebLogMaintenance.ps1 -SourceDirectories @("C:\Logs") -ArchiveDirectory "C:\Archive" -DaysOld 60 -WhatIf -Verbose
#>

# Function to write log messages with timestamp
function Write-LogMessage {
    param(
        [string]$Message,
        [string]$Level = "INFO"
    )
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    Write-Host "[$timestamp] [$Level] $Message"
}

# Function to create archive directory if it doesn't exist
function New-ArchiveDirectory {
    param([string]$Path)

    if (-not (Test-Path -Path $Path)) {
        try {
            New-Item -Path $Path -ItemType Directory -Force | Out-Null
            Write-LogMessage "Created archive directory: $Path"
        }
        catch {
            Write-LogMessage "Failed to create archive directory: $Path. Error: $($_.Exception.Message)" "ERROR"
            throw
        }
    }
}

# Function to get files to archive
function Get-FilesToArchive {
    param(
        [string[]]$Directories,
        [string[]]$Extensions,
        [int]$Days
    )

    $CutoffDate = (Get-Date).AddDays(-$Days)
    $FilesToArchive = @()

    foreach ($Directory in $Directories) {
        if (-not (Test-Path -Path $Directory)) {
            Write-LogMessage "Directory not found: $Directory" "WARNING"
            continue
        }

        Write-LogMessage "Searching directory and subdirectories: $Directory"

        foreach ($Extension in $Extensions) {
            try {
                $Files = Get-ChildItem -Path $Directory -Filter $Extension -File -Recurse |
                         Where-Object { $_.LastWriteTime -lt $CutoffDate }

                if ($Files) {
                    $FilesToArchive += $Files
                    Write-LogMessage "Found $($Files.Count) $Extension files older than $Days days in $Directory (including subdirectories)"
                }
            }
            catch {
                Write-LogMessage "Error searching for $Extension files in $Directory`: $($_.Exception.Message)" "ERROR"
            }
        }
    }

    return $FilesToArchive
}

# Function to create zip archive
function New-Archive {
    param(
        [System.IO.FileInfo[]]$Files,
        [string]$ArchivePath
    )

    try {
        # Load System.IO.Compression.FileSystem assembly
        Add-Type -AssemblyName System.IO.Compression.FileSystem

        # Create new zip archive
        $Archive = [System.IO.Compression.ZipFile]::Open($ArchivePath, [System.IO.Compression.ZipArchiveMode]::Create)

        foreach ($File in $Files) {
            # Create entry name that includes directory info to avoid conflicts
            $EntryName = "$($File.Directory.Name)_$($File.Name)"

            # Add file to archive
            $Entry = $Archive.CreateEntry($EntryName)
            $EntryStream = $Entry.Open()
            $FileStream = $File.OpenRead()

            $FileStream.CopyTo($EntryStream)

            $FileStream.Close()
            $EntryStream.Close()
        }

        $Archive.Dispose()
        Write-LogMessage "Created archive: $ArchivePath with $($Files.Count) files"
        return $true
    }
    catch {
        Write-LogMessage "Failed to create archive $ArchivePath`: $($_.Exception.Message)" "ERROR"
        if ($Archive) { $Archive.Dispose() }
        return $false
    }
}

# Function to delete archived files
function Remove-ArchivedFiles {
    param([System.IO.FileInfo[]]$Files)

    $DeletedCount = 0
    $ErrorCount = 0

    foreach ($File in $Files) {
        try {
            if ($WhatIf) {
                Write-LogMessage "WHATIF: Would delete file: $($File.FullName)"
            }
            else {
                Remove-Item -Path $File.FullName -Force
                Write-LogMessage "Deleted: $($File.FullName)" "INFO"
                $DeletedCount++
            }
        }
        catch {
            Write-LogMessage "Failed to delete $($File.FullName): $($_.Exception.Message)" "ERROR"
            $ErrorCount++
        }
    }

    return @{ Deleted = $DeletedCount; Errors = $ErrorCount }
}

# Main execution
try {
    Write-LogMessage "Starting WebLog Maintenance Script"
    Write-LogMessage "Parameters: DaysOld=$DaysOld, Extensions=$($FileExtensions -join ','), WhatIf=$WhatIf"
    
    # Validate and create archive directory
    if ($WhatIf) {
        Write-LogMessage "WHATIF: Would ensure archive directory exists: $ArchiveDirectory"
    }
    else {
        New-ArchiveDirectory -Path $ArchiveDirectory
    }

    # Get files to archive
    Write-LogMessage "Searching for files older than $DaysOld days..."
    $FilesToArchive = Get-FilesToArchive -Directories $SourceDirectories -Extensions $FileExtensions -Days $DaysOld

    if ($FilesToArchive.Count -eq 0) {
        Write-LogMessage "No files found to archive."
        exit 0
    }

    Write-LogMessage "Found $($FilesToArchive.Count) files to archive"

    if ($Verbose) {
        foreach ($File in $FilesToArchive) {
            Write-LogMessage "  - $($File.FullName) (Modified: $($File.LastWriteTime))"
        }
    }

    # Group files by directory for better organization
    $FileGroups = $FilesToArchive | Group-Object { $_.Directory.FullName }
    
    foreach ($Group in $FileGroups) {
        $DirectoryName = Split-Path $Group.Name -Leaf
        $Timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
        $ArchiveFileName = "LogArchive_${DirectoryName}_${Timestamp}.zip"
        $ArchivePath = Join-Path $ArchiveDirectory $ArchiveFileName

        Write-LogMessage "Processing $($Group.Count) files from directory: $($Group.Name)"

        if ($WhatIf) {
            Write-LogMessage "WHATIF: Would create archive: $ArchivePath"
            Write-LogMessage "WHATIF: Would archive $($Group.Count) files"
        }
        else {
            # Create archive
            $ArchiveSuccess = New-Archive -Files $Group.Group -ArchivePath $ArchivePath

            if ($ArchiveSuccess) {
                # Delete original files
                $DeleteResults = Remove-ArchivedFiles -Files $Group.Group
                Write-LogMessage "Deleted $($DeleteResults.Deleted) files, $($DeleteResults.Errors) errors"
            }
            else {
                Write-LogMessage "Skipping file deletion due to archive creation failure" "WARNING"
            }
        }
    }
    
    Write-LogMessage "WebLog Maintenance Script completed successfully"
}
catch {
    Write-LogMessage "Script execution failed: $($_.Exception.Message)" "ERROR"
    exit 1
}
