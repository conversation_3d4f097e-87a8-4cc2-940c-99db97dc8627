param(
    [Parameter(Mandatory=$true)]
    [string[]]$SourceDirectories,
    
    [Parameter(Mandatory=$true)]
    [string]$ArchiveDirectory,
    
    [Parameter(Mandatory=$false)]
    [int]$DaysOld = 30,
    
    [Parameter(Mandatory=$false)]
    [string[]]$FileExtensions = @("*.log", "*.trc"),
    
    [Parameter(Mandatory=$false)]
    [switch]$WhatIf,
    
    [Parameter(Mandatory=$false)]
    [switch]$Verbose
)

<#
.SYNOPSIS
    Archives old log and trace files from specified directories.

.DESCRIPTION
    This script searches specified directories for log and trace files older than a specified number of days,
    archives them into zip files in an archive directory, and then deletes the original files.

.PARAMETER SourceDirectories
    Array of directories to search for log files. Mandatory parameter.

.PARAMETER ArchiveDirectory
    Directory where archived zip files will be stored. Mandatory parameter.

.PARAMETER DaysOld
    Number of days old files must be to be archived. Default is 30 days.

.PARAMETER FileExtensions
    Array of file extensions to search for. Default is @("*.log", "*.trc").

.PARAMETER WhatIf
    Shows what would be done without actually performing the operations.

.PARAMETER Verbose
    Enables verbose output showing detailed information about operations.

.EXAMPLE
    .\WebLogMaintenance.ps1 -SourceDirectories @("C:\Logs", "C:\App\Logs") -ArchiveDirectory "C:\Archive"
    
.EXAMPLE
    .\WebLogMaintenance.ps1 -SourceDirectories @("C:\Logs") -ArchiveDirectory "C:\Archive" -DaysOld 60 -WhatIf -Verbose
#>

# Function to write log messages with timestamp
function Write-LogMessage {
    param(
        [string]$Message,
        [string]$Level = "INFO"
    )
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    Write-Host "[$timestamp] [$Level] $Message"
}

# Function to create archive directory if it doesn't exist
function Ensure-ArchiveDirectory {
    param([string]$Path)
    
    if (-not (Test-Path $Path)) {
        try {
            New-Item -Path $Path -ItemType Directory -Force | Out-Null
            Write-LogMessage "Created archive directory: $Path"
        }
        catch {
            Write-LogMessage "Failed to create archive directory: $Path. Error: $($_.Exception.Message)" "ERROR"
            throw
        }
    }
}

# Function to get files to archive
function Get-FilesToArchive {
    param(
        [string[]]$Directories,
        [string[]]$Extensions,
        [int]$Days
    )
    
    $cutoffDate = (Get-Date).AddDays(-$Days)
    $filesToArchive = @()
    
    foreach ($directory in $Directories) {
        if (-not (Test-Path $directory)) {
            Write-LogMessage "Directory not found: $directory" "WARNING"
            continue
        }
        
        Write-LogMessage "Searching directory: $directory"
        
        foreach ($extension in $Extensions) {
            try {
                $files = Get-ChildItem -Path $directory -Filter $extension -File -Recurse | 
                         Where-Object { $_.LastWriteTime -lt $cutoffDate }
                
                if ($files) {
                    $filesToArchive += $files
                    Write-LogMessage "Found $($files.Count) $extension files older than $Days days in $directory"
                }
            }
            catch {
                Write-LogMessage "Error searching for $extension files in $directory`: $($_.Exception.Message)" "ERROR"
            }
        }
    }
    
    return $filesToArchive
}

# Function to create zip archive
function Create-Archive {
    param(
        [System.IO.FileInfo[]]$Files,
        [string]$ArchivePath
    )
    
    try {
        # Load System.IO.Compression.FileSystem assembly
        Add-Type -AssemblyName System.IO.Compression.FileSystem
        
        # Create new zip archive
        $archive = [System.IO.Compression.ZipFile]::Open($ArchivePath, [System.IO.Compression.ZipArchiveMode]::Create)
        
        foreach ($file in $Files) {
            # Create relative path for the zip entry
            $relativePath = $file.FullName.Replace(':', '_').Replace('\', '_')
            $entryName = "$($file.Directory.Name)_$($file.Name)"
            
            # Add file to archive
            $entry = $archive.CreateEntry($entryName)
            $entryStream = $entry.Open()
            $fileStream = $file.OpenRead()
            
            $fileStream.CopyTo($entryStream)
            
            $fileStream.Close()
            $entryStream.Close()
        }
        
        $archive.Dispose()
        Write-LogMessage "Created archive: $ArchivePath with $($Files.Count) files"
        return $true
    }
    catch {
        Write-LogMessage "Failed to create archive $ArchivePath`: $($_.Exception.Message)" "ERROR"
        if ($archive) { $archive.Dispose() }
        return $false
    }
}

# Function to delete archived files
function Remove-ArchivedFiles {
    param([System.IO.FileInfo[]]$Files)
    
    $deletedCount = 0
    $errorCount = 0
    
    foreach ($file in $Files) {
        try {
            if ($WhatIf) {
                Write-LogMessage "WHATIF: Would delete file: $($file.FullName)"
            }
            else {
                Remove-Item -Path $file.FullName -Force
                Write-LogMessage "Deleted: $($file.FullName)" "INFO"
                $deletedCount++
            }
        }
        catch {
            Write-LogMessage "Failed to delete $($file.FullName): $($_.Exception.Message)" "ERROR"
            $errorCount++
        }
    }
    
    return @{ Deleted = $deletedCount; Errors = $errorCount }
}

# Main execution
try {
    Write-LogMessage "Starting WebLog Maintenance Script"
    Write-LogMessage "Parameters: DaysOld=$DaysOld, Extensions=$($FileExtensions -join ','), WhatIf=$WhatIf"
    
    # Validate and create archive directory
    if ($WhatIf) {
        Write-LogMessage "WHATIF: Would ensure archive directory exists: $ArchiveDirectory"
    }
    else {
        Ensure-ArchiveDirectory -Path $ArchiveDirectory
    }
    
    # Get files to archive
    Write-LogMessage "Searching for files older than $DaysOld days..."
    $filesToArchive = Get-FilesToArchive -Directories $SourceDirectories -Extensions $FileExtensions -Days $DaysOld
    
    if ($filesToArchive.Count -eq 0) {
        Write-LogMessage "No files found to archive."
        exit 0
    }
    
    Write-LogMessage "Found $($filesToArchive.Count) files to archive"
    
    if ($Verbose) {
        foreach ($file in $filesToArchive) {
            Write-LogMessage "  - $($file.FullName) (Modified: $($file.LastWriteTime))"
        }
    }
    
    # Group files by directory for better organization
    $fileGroups = $filesToArchive | Group-Object { $_.Directory.FullName }
    
    foreach ($group in $fileGroups) {
        $directoryName = Split-Path $group.Name -Leaf
        $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
        $archiveFileName = "LogArchive_${directoryName}_${timestamp}.zip"
        $archivePath = Join-Path $ArchiveDirectory $archiveFileName
        
        Write-LogMessage "Processing $($group.Count) files from directory: $($group.Name)"
        
        if ($WhatIf) {
            Write-LogMessage "WHATIF: Would create archive: $archivePath"
            Write-LogMessage "WHATIF: Would archive $($group.Count) files"
        }
        else {
            # Create archive
            $archiveSuccess = Create-Archive -Files $group.Group -ArchivePath $archivePath
            
            if ($archiveSuccess) {
                # Delete original files
                $deleteResults = Remove-ArchivedFiles -Files $group.Group
                Write-LogMessage "Deleted $($deleteResults.Deleted) files, $($deleteResults.Errors) errors"
            }
            else {
                Write-LogMessage "Skipping file deletion due to archive creation failure" "WARNING"
            }
        }
    }
    
    Write-LogMessage "WebLog Maintenance Script completed successfully"
}
catch {
    Write-LogMessage "Script execution failed: $($_.Exception.Message)" "ERROR"
    exit 1
}
